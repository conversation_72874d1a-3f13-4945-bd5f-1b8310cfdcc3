import pandas as pd
from openpyxl import load_workbook
import os

# 检查 Excel 文件格式化
excel_file = 'C:\\Results\\test_results.xlsx'

if os.path.exists(excel_file):
    print(f"检查 Excel 文件格式化: {excel_file}")
    
    # 使用 openpyxl 读取文件以检查格式化
    workbook = load_workbook(excel_file)
    worksheet = workbook['测试详情']
    
    print("\n=== 检查单元格格式化 ===")
    
    # 读取数据以了解内容
    df = pd.read_excel(excel_file, sheet_name='测试详情')
    print(f"总记录数: {len(df)}")
    
    # 检查每一行的格式化
    for row_idx in range(2, len(df) + 2):  # 从第2行开始（第1行是标题）
        # 获取 result 列的值
        result_cell = worksheet.cell(row=row_idx, column=df.columns.get_loc('result') + 1)
        result_value = result_cell.value
        
        # 检查第一个单元格的背景色
        first_cell = worksheet.cell(row=row_idx, column=1)
        fill_color = first_cell.fill.start_color.rgb if first_cell.fill.start_color else None
        
        print(f"行 {row_idx}: result={result_value}, 背景色={fill_color}")
        
        # 验证 FAIL 记录是否有红色背景
        if result_value == 'FAIL':
            if fill_color and (fill_color.upper() == 'FFCCCC' or fill_color.upper() == '00FFCCCC'):
                print(f"  ✅ FAIL 记录正确标记为红色")
            else:
                print(f"  ❌ FAIL 记录未正确标记为红色")
        elif result_value == 'PASS':
            if not fill_color or fill_color == '00000000':
                print(f"  ✅ PASS 记录无背景色（正确）")
            else:
                print(f"  ⚠️  PASS 记录有背景色: {fill_color}")
    
    workbook.close()
    
else:
    print(f"Excel 文件不存在: {excel_file}")
