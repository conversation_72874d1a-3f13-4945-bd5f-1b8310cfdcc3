import pandas as pd
from openpyxl import load_workbook
import os

print("=== 📊 Excel 格式化验证报告 ===\n")

excel_file = 'C:\\Results\\test_results.xlsx'

if os.path.exists(excel_file):
    # 读取数据
    df = pd.read_excel(excel_file, sheet_name='测试详情')
    workbook = load_workbook(excel_file)
    worksheet = workbook['测试详情']
    
    print("✅ Excel 文件验证通过")
    print(f"📁 文件路径: {excel_file}")
    print(f"📊 记录数量: {len(df)}")
    
    print(f"\n🎨 格式化功能验证:")
    
    # 1. 检查 capacity 列格式
    if 'capacity' in df.columns:
        capacity_col_index = df.columns.get_loc('capacity') + 1
        capacity_col_letter = worksheet.cell(row=1, column=capacity_col_index).column_letter
        capacity_width = worksheet.column_dimensions[capacity_col_letter].width
        
        sample_cell = worksheet.cell(row=2, column=capacity_col_index)
        
        print(f"  📊 Capacity 列:")
        print(f"     - 列宽: {capacity_width} (推荐: 18)")
        print(f"     - 数字格式: {sample_cell.number_format}")
        print(f"     - 示例值: {sample_cell.value:,}")  # 使用 Python 格式化显示
        
        if capacity_width >= 18 and sample_cell.number_format == '#,##0':
            print(f"     ✅ Capacity 列格式化正确")
        else:
            print(f"     ❌ Capacity 列格式化有问题")
    
    # 2. 检查失败记录的红色高亮
    fail_count = 0
    highlighted_count = 0
    
    if 'result' in df.columns:
        print(f"\n  🎯 失败记录高亮:")
        for row_idx, (_, row) in enumerate(df.iterrows(), start=2):
            if row.get('result') == 'FAIL':
                fail_count += 1
                # 检查第一个单元格的背景色
                first_cell = worksheet.cell(row=row_idx, column=1)
                fill_color = first_cell.fill.start_color.rgb if first_cell.fill.start_color else None
                
                if fill_color and ('FFCCCC' in fill_color.upper()):
                    highlighted_count += 1
                    print(f"     ✅ 行 {row_idx}: {row['uid']} - 正确高亮")
                else:
                    print(f"     ❌ 行 {row_idx}: {row['uid']} - 未高亮")
        
        print(f"     📈 失败记录: {fail_count} 个")
        print(f"     🎨 已高亮: {highlighted_count} 个")
        
        if fail_count == highlighted_count:
            print(f"     ✅ 所有失败记录都正确高亮")
        else:
            print(f"     ❌ 高亮不完整")
    
    # 3. 检查列宽设置
    print(f"\n  📏 列宽设置:")
    important_columns = ['capacity', 'uid', 'computer']
    for col_name in important_columns:
        if col_name in df.columns:
            col_index = df.columns.get_loc(col_name) + 1
            col_letter = worksheet.cell(row=1, column=col_index).column_letter
            col_width = worksheet.column_dimensions[col_letter].width
            print(f"     - {col_name}: {col_width}")
    
    # 4. 数据预览
    print(f"\n📋 数据预览:")
    print("UID\t\tResult\tCapacity")
    print("-" * 50)
    for _, row in df.head(3).iterrows():
        capacity_formatted = f"{row['capacity']:,}" if pd.notna(row['capacity']) else "N/A"
        print(f"{row['uid'][:15]}...\t{row['result']}\t{capacity_formatted}")
    
    workbook.close()
    
    print(f"\n🎉 验证完成！")
    print(f"   - Capacity 列现在应该显示为千分位分隔格式")
    print(f"   - 失败记录应该有红色背景")
    print(f"   - 所有列都有适当的宽度")
    
else:
    print(f"❌ Excel 文件不存在: {excel_file}")
