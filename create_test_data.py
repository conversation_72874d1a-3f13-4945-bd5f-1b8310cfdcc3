import json
import os

# 创建测试数据目录
test_dir = 'C:\\TempDownloads'
if not os.path.exists(test_dir):
    os.makedirs(test_dir)

# 清理现有的测试文件
for file in os.listdir(test_dir):
    if file.endswith('.txt') and file != 'process_test_results.txt':
        os.remove(os.path.join(test_dir, file))

# 创建测试数据
test_data = [
    {
        "batchid": "20250826",
        "capacity": 3452816845,
        "computer": "DESKTOP-TEST1",
        "mode": "AUTO",
        "result": "PASS",
        "slc": "false",
        "time": "20250826100000",
        "uid": "TEST001",
        "ulevel": "HIGH"
    },
    {
        "batchid": "20250826",
        "capacity": 0,
        "computer": "DESKTOP-TEST2",
        "mode": "MANUAL",
        "port": 8,
        "result": "FAIL",
        "slc": "false",
        "time": "20250826101000",
        "uid": "TEST002",
        "ulevel": "LOW"
    },
    {
        "batchid": "20250826",
        "capacity": 2000000000,
        "computer": "DESKTOP-TEST3",
        "mode": "AUTO",
        "result": "PASS",
        "slc": "true",
        "time": "20250826102000",
        "uid": "TEST003",
        "ulevel": "MEDIUM"
    },
    {
        "batchid": "20250826",
        "capacity": 500000000,
        "computer": "DESKTOP-TEST4",
        "mode": "MANUAL",
        "port": 4,
        "result": "FAIL",
        "slc": "false",
        "time": "20250826103000",
        "uid": "TEST004",
        "ulevel": "HIGH"
    },
    {
        "batchid": "20250826",
        "capacity": 1800000000,
        "computer": "DESKTOP-TEST5",
        "mode": "AUTO",
        "result": "PASS",
        "slc": "true",
        "time": "20250826104000",
        "uid": "TEST005",
        "ulevel": "HIGH"
    }
]

# 保存测试数据到文件
for i, data in enumerate(test_data):
    filename = f"test_data_{i+1:03d}.txt"
    filepath = os.path.join(test_dir, filename)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False)
    print(f"创建测试文件: {filename}")

print(f"\n总共创建了 {len(test_data)} 个测试文件")
print("其中:")
print(f"- PASS: {sum(1 for d in test_data if d['result'] == 'PASS')} 个")
print(f"- FAIL: {sum(1 for d in test_data if d['result'] == 'FAIL')} 个")
