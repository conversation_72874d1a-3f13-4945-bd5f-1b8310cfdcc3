import pandas as pd
from openpyxl import load_workbook
import os

# 检查 Excel 文件中的列宽设置
excel_file = 'C:\\Results\\test_results.xlsx'

if os.path.exists(excel_file):
    print(f"检查 Excel 文件列宽设置: {excel_file}")
    
    # 使用 openpyxl 检查列宽
    workbook = load_workbook(excel_file)
    worksheet = workbook['测试详情']
    
    # 读取数据以了解列结构
    df = pd.read_excel(excel_file, sheet_name='测试详情')
    
    print(f"\n📊 列宽设置检查:")
    print(f"{'列名':<12} {'列字母':<6} {'设置宽度':<10} {'状态'}")
    print("-" * 40)
    
    for col_name in df.columns:
        col_index = df.columns.get_loc(col_name) + 1
        col_letter = worksheet.cell(row=1, column=col_index).column_letter
        col_width = worksheet.column_dimensions[col_letter].width
        
        # 检查是否设置了合适的宽度
        if col_name == 'capacity' and col_width >= 18:
            status = "✅ 足够宽"
        elif col_name == 'uid' and col_width >= 30:
            status = "✅ 足够宽"
        elif col_width and col_width > 8:
            status = "✅ 已设置"
        elif col_width:
            status = "⚠️ 可能太窄"
        else:
            status = "❌ 未设置"
            
        print(f"{col_name:<12} {col_letter:<6} {col_width or '默认':<10} {status}")
    
    print(f"\n🔍 capacity 列详细检查:")
    if 'capacity' in df.columns:
        capacity_col_index = df.columns.get_loc('capacity') + 1
        capacity_col_letter = worksheet.cell(row=1, column=capacity_col_index).column_letter
        capacity_width = worksheet.column_dimensions[capacity_col_letter].width
        
        print(f"  - 列位置: {capacity_col_letter} (第 {capacity_col_index} 列)")
        print(f"  - 设置宽度: {capacity_width}")
        print(f"  - 推荐宽度: 18 (用于显示如 '3,452,816,845')")
        
        # 检查数字格式
        sample_cell = worksheet.cell(row=2, column=capacity_col_index)
        print(f"  - 数字格式: {sample_cell.number_format}")
        print(f"  - 原始值: {sample_cell.value}")
        
        if capacity_width >= 18:
            print(f"  ✅ 列宽足够，应该能正确显示大数字")
        else:
            print(f"  ❌ 列宽不足，可能显示为 ########")
    
    workbook.close()
    
else:
    print(f"Excel 文件不存在: {excel_file}")
