import pandas as pd
import os

# 检查 Excel 文件
excel_file = 'C:\\Results\\test_results.xlsx'

if os.path.exists(excel_file):
    print(f"Excel 文件存在: {excel_file}")
    
    # 读取两个工作表
    try:
        df_details = pd.read_excel(excel_file, sheet_name='测试详情')
        df_summary = pd.read_excel(excel_file, sheet_name='统计良率')
        
        print("\n=== 测试详情工作表 ===")
        print(f"记录数: {len(df_details)}")
        print("列名:", list(df_details.columns))
        print("\n前几行数据:")
        print(df_details.head())
        
        print("\n=== 统计良率工作表 ===")
        print(f"记录数: {len(df_summary)}")
        print("内容:")
        print(df_summary)
        
    except Exception as e:
        print(f"读取 Excel 文件时出错: {e}")
else:
    print(f"Excel 文件不存在: {excel_file}")
