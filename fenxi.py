import ftplib
import json
import pandas as pd
import os
import time

# FTP 配置
FTP_HOST = '*************'
FTP_PORT = 21
FTP_USER = 'test'
FTP_PASS = ''
FTP_DIR = '/TestLog/TestBatchInfo/20250826'

# 本地下载目录
LOCAL_DIR = 'C:\\TempDownloads'
OUTPUT_EXCEL = 'C:\\Results\\test_results.xlsx'
if not os.path.exists(LOCAL_DIR):
    os.makedirs(LOCAL_DIR)
if not os.path.exists(os.path.dirname(OUTPUT_EXCEL)):
    os.makedirs(os.path.dirname(OUTPUT_EXCEL))

# 下载所有 .txt 文件
def download_files():
    ftp = ftplib.FTP()
    ftp.connect(FTP_HOST, FTP_PORT)
    ftp.login(user=FTP_USER, passwd=FTP_PASS)
    ftp.cwd(FTP_DIR)
    
    files = ftp.nlst()
    downloaded_files = []
    for file in files:
        if file.endswith('.txt'):
            local_path = os.path.join(LOCAL_DIR, file)
            with open(local_path, 'wb') as f:
                ftp.retrbinary(f'RETR {file}', f.write)
            downloaded_files.append(local_path)
            ftp.delete(file)  # 删除已下载的文件
    ftp.quit()
    return downloaded_files

# 解析文件并收集记录
def collect_records(downloaded_files):
    records = []
    for file_path in downloaded_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            try:
                data = json.loads(f.read())
                records.append(data)
            except json.JSONDecodeError:
                print(f"跳过无效 JSON 文件: {file_path}")
    return records

# 去重：按 UID 取最后时间
def deduplicate_records(records):
    record_dict = {}
    for record in records:
        uid = record.get('uid')
        time_str = record.get('time')
        if uid and time_str:
            if uid not in record_dict or time_str > record_dict[uid]['time']:
                record_dict[uid] = record
    return list(record_dict.values())

# 计算良率
def calculate_yield(records):
    total = len(records)
    if total == 0:
        return 0.0
    passed = sum(1 for r in records if r.get('result') == 'PASS')
    return (passed / total) * 100

# 生成 Excel
def generate_excel(records, yield_rate):
    # 测试详情
    df_details = pd.DataFrame(records)
    df_details = df_details.sort_values(by='time', ascending=False)
    
    # 统计良率
    df_summary = pd.DataFrame({
        '指标': ['总测试数', '通过数', '良率 (%)'],
        '值': [len(records), sum(1 for r in records if r.get('result') == 'PASS'), yield_rate]
    })
    
    with pd.ExcelWriter(OUTPUT_EXCEL, engine='openpyxl') as writer:
        df_details.to_excel(writer, sheet_name='测试详情', index=False)
        df_summary.to_excel(writer, sheet_name='统计良率', index=False)

# 主函数
def main():
    try:
        import pandas as pd
        print("pandas 导入成功，版本：", pd.__version__)
    except ImportError:
        print("错误：未找到 pandas 库。请运行 `pip install pandas` 安装。")
        return
    
    try:
        downloaded_files = download_files()
        records = collect_records(downloaded_files)
        unique_records = deduplicate_records(records)
        yield_rate = calculate_yield(unique_records)
        generate_excel(unique_records, yield_rate)
        print(f"Excel 文件已生成: {OUTPUT_EXCEL}")
    except Exception as e:
        print(f"处理失败: {str(e)}")

if __name__ == '__main__':
    main()