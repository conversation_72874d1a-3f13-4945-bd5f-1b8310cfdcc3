import ftplib
import json
import pandas as pd
import os
import time

# FTP 配置
FTP_HOST = '*************'
FTP_PORT = 21
FTP_USER = 'test'
FTP_PASS = ''
FTP_DIR = '/TestLog/TestBatchInfo/20250826'

# 本地下载目录
LOCAL_DIR = 'C:\\TempDownloads'
OUTPUT_EXCEL = 'C:\\Results\\test_results.xlsx'
if not os.path.exists(LOCAL_DIR):
    os.makedirs(LOCAL_DIR)
if not os.path.exists(os.path.dirname(OUTPUT_EXCEL)):
    os.makedirs(os.path.dirname(OUTPUT_EXCEL))

# 下载所有 .txt 文件
def download_files():
    downloaded_files = []

    # 首先检查本地目录中是否已有文件
    if os.path.exists(LOCAL_DIR):
        local_files = [f for f in os.listdir(LOCAL_DIR) if f.endswith('.txt')]
        if local_files:
            print(f"发现本地文件 {len(local_files)} 个，直接处理本地文件")
            for file in local_files:
                local_path = os.path.join(LOCAL_DIR, file)
                downloaded_files.append(local_path)
            return downloaded_files

    # 如果本地没有文件，尝试从 FTP 下载
    try:
        ftp = ftplib.FTP()
        ftp.connect(FTP_HOST, FTP_PORT)
        ftp.login(user=FTP_USER, passwd=FTP_PASS)
        ftp.cwd(FTP_DIR)

        files = ftp.nlst()
        for file in files:
            if file.endswith('.txt'):
                local_path = os.path.join(LOCAL_DIR, file)
                with open(local_path, 'wb') as f:
                    ftp.retrbinary(f'RETR {file}', f.write)
                downloaded_files.append(local_path)
                ftp.delete(file)  # 删除已下载的文件
        ftp.quit()
    except Exception as e:
        print(f"FTP 连接失败: {e}")
        print("将尝试处理本地已存在的文件")

    return downloaded_files

# 解析文件并收集记录
def collect_records(downloaded_files):
    records = []
    for file_path in downloaded_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            try:
                data = json.loads(f.read())
                records.append(data)
            except json.JSONDecodeError:
                print(f"跳过无效 JSON 文件: {file_path}")
    return records

# 去重：按 UID 取最后时间
def deduplicate_records(records):
    record_dict = {}
    for record in records:
        uid = record.get('uid')
        time_str = record.get('time', '')  # 如果没有时间字段，使用空字符串
        if uid:
            if uid not in record_dict or (time_str and time_str > record_dict[uid].get('time', '')):
                record_dict[uid] = record
    return list(record_dict.values())

# 计算良率
def calculate_yield(records):
    total = len(records)
    if total == 0:
        return 0.0
    passed = sum(1 for r in records if r.get('result') == 'PASS')
    return (passed / total) * 100

# 生成 Excel
def generate_excel(records, yield_rate):
    from openpyxl.styles import PatternFill

    # 测试详情
    df_details = pd.DataFrame(records)
    # 只有当 time 列存在时才排序
    if 'time' in df_details.columns:
        df_details = df_details.sort_values(by='time', ascending=False)

    # 统计良率
    df_summary = pd.DataFrame({
        '指标': ['总测试数', '通过数', '良率 (%)'],
        '值': [len(records), sum(1 for r in records if r.get('result') == 'PASS'), yield_rate]
    })

    with pd.ExcelWriter(OUTPUT_EXCEL, engine='openpyxl') as writer:
        df_details.to_excel(writer, sheet_name='测试详情', index=False)
        df_summary.to_excel(writer, sheet_name='统计良率', index=False)

        # 获取工作表对象
        worksheet = writer.sheets['测试详情']

        # 定义红色填充样式
        red_fill = PatternFill(start_color='FFCCCC', end_color='FFCCCC', fill_type='solid')

        # 设置 capacity 列的数字格式（如果存在）
        if 'capacity' in df_details.columns:
            capacity_col_index = df_details.columns.get_loc('capacity') + 1  # Excel 列索引从1开始
            for row_idx in range(2, len(df_details) + 2):  # 从第2行开始（第1行是标题）
                cell = worksheet.cell(row=row_idx, column=capacity_col_index)
                cell.number_format = '#,##0'  # 设置为千分位分隔的整数格式

        # 检查是否有 result 列
        if 'result' in df_details.columns:
            # 遍历数据行，为失败的测试添加红色背景
            for row_idx, (_, row) in enumerate(df_details.iterrows(), start=2):  # 从第2行开始（第1行是标题）
                if row.get('result') == 'FAIL':
                    # 为整行添加红色背景
                    for col_idx in range(1, len(df_details.columns) + 1):
                        cell = worksheet.cell(row=row_idx, column=col_idx)
                        cell.fill = red_fill

# 主函数
def main():
    try:
        import pandas as pd
        print("pandas 导入成功，版本：", pd.__version__)
    except ImportError:
        print("错误：未找到 pandas 库。请运行 `pip install pandas` 安装。")
        return

    try:
        print("开始下载文件...")
        downloaded_files = download_files()
        print(f"下载了 {len(downloaded_files)} 个文件: {downloaded_files}")

        print("开始收集记录...")
        records = collect_records(downloaded_files)
        print(f"收集了 {len(records)} 条记录")

        print("开始去重...")
        unique_records = deduplicate_records(records)
        print(f"去重后有 {len(unique_records)} 条记录")

        print("计算良率...")
        yield_rate = calculate_yield(unique_records)
        print(f"良率: {yield_rate:.2f}%")

        print("生成 Excel...")
        generate_excel(unique_records, yield_rate)
        print(f"Excel 文件已生成: {OUTPUT_EXCEL}")
    except Exception as e:
        print(f"处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()