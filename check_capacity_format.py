import pandas as pd
from openpyxl import load_workbook
import os

# 检查 Excel 文件中 capacity 列的格式
excel_file = 'C:\\Results\\test_results.xlsx'

if os.path.exists(excel_file):
    print(f"检查 Excel 文件中 capacity 列的格式: {excel_file}")
    
    # 使用 pandas 读取数据
    df = pd.read_excel(excel_file, sheet_name='测试详情')
    print(f"\n📊 使用 pandas 读取的 capacity 数据:")
    for idx, row in df.iterrows():
        print(f"  行 {idx+2}: UID={row['uid']}, capacity={row['capacity']}")
    
    # 使用 openpyxl 检查格式化
    workbook = load_workbook(excel_file)
    worksheet = workbook['测试详情']
    
    print(f"\n🔍 检查 Excel 单元格格式:")
    
    if 'capacity' in df.columns:
        capacity_col_index = df.columns.get_loc('capacity') + 1
        print(f"capacity 列位于第 {capacity_col_index} 列")
        
        for row_idx in range(2, len(df) + 2):  # 从第2行开始
            cell = worksheet.cell(row=row_idx, column=capacity_col_index)
            uid_cell = worksheet.cell(row=row_idx, column=df.columns.get_loc('uid') + 1)
            
            print(f"  行 {row_idx}: UID={uid_cell.value}")
            print(f"    - 原始值: {cell.value}")
            print(f"    - 数字格式: {cell.number_format}")
            print(f"    - 显示值: {cell.displayed_value if hasattr(cell, 'displayed_value') else '无法获取'}")
    
    workbook.close()
    
    print(f"\n✨ 格式说明:")
    print("  - '#,##0' 格式会将数字显示为千分位分隔的整数")
    print("  - 例如: 3452816845 显示为 3,452,816,845")
    print("  - 这样比科学计数法 (3.45E+09) 更直观")
    
else:
    print(f"Excel 文件不存在: {excel_file}")
